# Dot Case

[![NPM version][npm-image]][npm-url]
[![NPM downloads][downloads-image]][downloads-url]
[![Bundle size][bundlephobia-image]][bundlephobia-url]

> Transform into a lower case string with a period between words.

## Installation

```
npm install dot-case --save
```

## Usage

```js
import { dotCase } from "dot-case";

dotCase("string"); //=> "string"
dotCase("dot.case"); //=> "dot.case"
dotCase("PascalCase"); //=> "pascal.case"
dotCase("version 1.2.10"); //=> "version.1.2.10"
```

The function also accepts [`options`](https://github.com/blakeembrey/change-case#options).

## License

MIT

[npm-image]: https://img.shields.io/npm/v/dot-case.svg?style=flat
[npm-url]: https://npmjs.org/package/dot-case
[downloads-image]: https://img.shields.io/npm/dm/dot-case.svg?style=flat
[downloads-url]: https://npmjs.org/package/dot-case
[bundlephobia-image]: https://img.shields.io/bundlephobia/minzip/dot-case.svg
[bundlephobia-url]: https://bundlephobia.com/result?p=dot-case
