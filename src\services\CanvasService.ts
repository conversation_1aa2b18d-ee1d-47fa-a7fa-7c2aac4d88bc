// خدمة Canvas مع دعم متعدد للمكتبات
export class CanvasService {
  private static instance: CanvasService;
  private canvasType: 'node-canvas' | 'fabric' | 'html5' = 'html5';
  private nodeCanvas: any = null;

  private constructor() {
    this.initializeCanvas();
  }

  public static getInstance(): CanvasService {
    if (!CanvasService.instance) {
      CanvasService.instance = new CanvasService();
    }
    return CanvasService.instance;
  }

  private initializeCanvas() {
    // محاولة استخدام node-canvas للمعالجة المتقدمة
    try {
      this.nodeCanvas = require('canvas');
      this.canvasType = 'node-canvas';
      console.log('✅ Using node-canvas for advanced image processing');
      return;
    } catch (error) {
      console.log('⚠️ node-canvas not available, using HTML5 canvas...');
    }

    // استخدام HTML5 Canvas كحل افتراضي
    this.canvasType = 'html5';
    console.log('✅ Using HTML5 canvas');
  }

  // إنشاء canvas جديد
  public createCanvas(width: number, height: number): any {
    if (this.canvasType === 'node-canvas' && this.nodeCanvas) {
      return this.nodeCanvas.createCanvas(width, height);
    } else {
      // إنشاء HTML5 canvas
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      return canvas;
    }
  }

  // إنشاء context للرسم
  public getContext(canvas: any, type: '2d' | 'webgl' = '2d'): any {
    if (this.canvasType === 'node-canvas') {
      return canvas.getContext(type);
    } else {
      return canvas.getContext(type);
    }
  }

  // تحويل الصورة إلى Buffer (للـ node-canvas)
  public toBuffer(canvas: any, format: 'png' | 'jpeg' = 'png'): Buffer | null {
    if (this.canvasType === 'node-canvas') {
      return canvas.toBuffer(`image/${format}`);
    }
    return null;
  }

  // تحويل الصورة إلى Data URL
  public toDataURL(canvas: any, format: 'png' | 'jpeg' = 'png', quality?: number): string {
    if (format === 'jpeg' && quality) {
      return canvas.toDataURL(`image/${format}`, quality);
    }
    return canvas.toDataURL(`image/${format}`);
  }

  // تحميل صورة
  public async loadImage(src: string): Promise<any> {
    if (this.canvasType === 'node-canvas' && this.nodeCanvas) {
      return this.nodeCanvas.loadImage(src);
    } else {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = src;
      });
    }
  }

  // رسم صورة على Canvas
  public drawImage(
    ctx: any,
    image: any,
    sx: number = 0,
    sy: number = 0,
    sWidth?: number,
    sHeight?: number,
    dx?: number,
    dy?: number,
    dWidth?: number,
    dHeight?: number
  ): void {
    if (arguments.length === 3) {
      ctx.drawImage(image, sx, sy);
    } else if (arguments.length === 5) {
      ctx.drawImage(image, sx, sy, sWidth, sHeight);
    } else if (arguments.length === 9) {
      ctx.drawImage(image, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight);
    }
  }

  // رسم نص
  public drawText(
    ctx: any,
    text: string,
    x: number,
    y: number,
    options: {
      font?: string;
      fillStyle?: string;
      strokeStyle?: string;
      textAlign?: CanvasTextAlign;
      textBaseline?: CanvasTextBaseline;
      maxWidth?: number;
    } = {}
  ): void {
    // تطبيق الخيارات
    if (options.font) ctx.font = options.font;
    if (options.fillStyle) ctx.fillStyle = options.fillStyle;
    if (options.strokeStyle) ctx.strokeStyle = options.strokeStyle;
    if (options.textAlign) ctx.textAlign = options.textAlign;
    if (options.textBaseline) ctx.textBaseline = options.textBaseline;

    // رسم النص
    if (options.fillStyle) {
      if (options.maxWidth) {
        ctx.fillText(text, x, y, options.maxWidth);
      } else {
        ctx.fillText(text, x, y);
      }
    }

    if (options.strokeStyle) {
      if (options.maxWidth) {
        ctx.strokeText(text, x, y, options.maxWidth);
      } else {
        ctx.strokeText(text, x, y);
      }
    }
  }

  // رسم دائرة
  public drawCircle(
    ctx: any,
    x: number,
    y: number,
    radius: number,
    options: {
      fillStyle?: string;
      strokeStyle?: string;
      lineWidth?: number;
    } = {}
  ): void {
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, 2 * Math.PI);

    if (options.fillStyle) {
      ctx.fillStyle = options.fillStyle;
      ctx.fill();
    }

    if (options.strokeStyle) {
      ctx.strokeStyle = options.strokeStyle;
      if (options.lineWidth) ctx.lineWidth = options.lineWidth;
      ctx.stroke();
    }
  }

  // رسم مستطيل
  public drawRectangle(
    ctx: any,
    x: number,
    y: number,
    width: number,
    height: number,
    options: {
      fillStyle?: string;
      strokeStyle?: string;
      lineWidth?: number;
    } = {}
  ): void {
    if (options.fillStyle) {
      ctx.fillStyle = options.fillStyle;
      ctx.fillRect(x, y, width, height);
    }

    if (options.strokeStyle) {
      ctx.strokeStyle = options.strokeStyle;
      if (options.lineWidth) ctx.lineWidth = options.lineWidth;
      ctx.strokeRect(x, y, width, height);
    }
  }

  // رسم خط
  public drawLine(
    ctx: any,
    x1: number,
    y1: number,
    x2: number,
    y2: number,
    options: {
      strokeStyle?: string;
      lineWidth?: number;
      lineCap?: CanvasLineCap;
    } = {}
  ): void {
    ctx.beginPath();
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);

    if (options.strokeStyle) ctx.strokeStyle = options.strokeStyle;
    if (options.lineWidth) ctx.lineWidth = options.lineWidth;
    if (options.lineCap) ctx.lineCap = options.lineCap;

    ctx.stroke();
  }

  // تطبيق فلتر على الصورة
  public applyFilter(
    canvas: any,
    filter: 'blur' | 'brightness' | 'contrast' | 'grayscale' | 'sepia',
    value: number = 1
  ): any {
    const ctx = this.getContext(canvas);
    
    switch (filter) {
      case 'blur':
        ctx.filter = `blur(${value}px)`;
        break;
      case 'brightness':
        ctx.filter = `brightness(${value})`;
        break;
      case 'contrast':
        ctx.filter = `contrast(${value})`;
        break;
      case 'grayscale':
        ctx.filter = `grayscale(${value})`;
        break;
      case 'sepia':
        ctx.filter = `sepia(${value})`;
        break;
    }

    return canvas;
  }

  // تغيير حجم الصورة
  public resizeImage(
    sourceCanvas: any,
    newWidth: number,
    newHeight: number
  ): any {
    const resizedCanvas = this.createCanvas(newWidth, newHeight);
    const ctx = this.getContext(resizedCanvas);
    
    ctx.drawImage(sourceCanvas, 0, 0, newWidth, newHeight);
    
    return resizedCanvas;
  }

  // دمج عدة صور
  public mergeImages(images: Array<{canvas: any, x: number, y: number}>): any {
    if (images.length === 0) return null;

    // حساب الحجم المطلوب
    let maxWidth = 0;
    let maxHeight = 0;

    images.forEach(img => {
      maxWidth = Math.max(maxWidth, img.x + img.canvas.width);
      maxHeight = Math.max(maxHeight, img.y + img.canvas.height);
    });

    const mergedCanvas = this.createCanvas(maxWidth, maxHeight);
    const ctx = this.getContext(mergedCanvas);

    // رسم جميع الصور
    images.forEach(img => {
      ctx.drawImage(img.canvas, img.x, img.y);
    });

    return mergedCanvas;
  }

  // الحصول على معلومات الخدمة
  public getInfo(): any {
    return {
      type: this.canvasType,
      nodeCanvasAvailable: this.nodeCanvas !== null,
      features: {
        imageProcessing: this.canvasType === 'node-canvas',
        webGL: this.canvasType === 'html5',
        serverSideRendering: this.canvasType === 'node-canvas'
      }
    };
  }
}

// إتاحة الخدمة عالمياً
if (typeof window !== 'undefined') {
  (window as any).canvasService = CanvasService.getInstance();
}

export default CanvasService;
