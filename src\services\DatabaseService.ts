// خدمة قاعدة البيانات مع دعم sqlite3 و better-sqlite3
export class DatabaseService {
  private static instance: DatabaseService;
  private db: any = null;
  private dbType: 'sqlite3' | 'better-sqlite3' | 'memory' = 'memory';

  private constructor() {
    this.initializeDatabase();
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  private async initializeDatabase() {
    // محاولة استخدام sqlite3 أولاً
    try {
      const sqlite3 = require('sqlite3').verbose();
      this.db = new sqlite3.Database(':memory:');
      this.dbType = 'sqlite3';
      console.log('✅ Using sqlite3 database');
      await this.createTables();
      return;
    } catch (error) {
      console.log('⚠️ sqlite3 not available, trying better-sqlite3...');
    }

    // محاولة استخدام better-sqlite3 كبديل
    try {
      const Database = require('better-sqlite3');
      this.db = new Database(':memory:');
      this.dbType = 'better-sqlite3';
      console.log('✅ Using better-sqlite3 database');
      await this.createTables();
      return;
    } catch (error) {
      console.log('⚠️ better-sqlite3 not available, using memory storage...');
    }

    // استخدام تخزين في الذاكرة كحل أخير
    this.db = {
      projects: new Map(),
      matches: new Map(),
      events: new Map(),
      players: new Map(),
      teams: new Map()
    };
    this.dbType = 'memory';
    console.log('✅ Using in-memory storage');
  }

  private async createTables() {
    const tables = {
      projects: `
        CREATE TABLE IF NOT EXISTS projects (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          data TEXT
        )
      `,
      matches: `
        CREATE TABLE IF NOT EXISTS matches (
          id TEXT PRIMARY KEY,
          project_id TEXT,
          name TEXT NOT NULL,
          date DATETIME,
          home_team TEXT,
          away_team TEXT,
          video_path TEXT,
          duration INTEGER,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (project_id) REFERENCES projects (id)
        )
      `,
      events: `
        CREATE TABLE IF NOT EXISTS events (
          id TEXT PRIMARY KEY,
          match_id TEXT,
          type TEXT NOT NULL,
          timestamp REAL NOT NULL,
          player_id TEXT,
          team_id TEXT,
          x REAL,
          y REAL,
          data TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (match_id) REFERENCES matches (id)
        )
      `,
      players: `
        CREATE TABLE IF NOT EXISTS players (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          number INTEGER,
          position TEXT,
          team_id TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `,
      teams: `
        CREATE TABLE IF NOT EXISTS teams (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          color TEXT,
          logo TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `
    };

    if (this.dbType === 'sqlite3') {
      for (const [tableName, sql] of Object.entries(tables)) {
        await new Promise((resolve, reject) => {
          this.db.run(sql, (err: any) => {
            if (err) reject(err);
            else resolve(true);
          });
        });
      }
    } else if (this.dbType === 'better-sqlite3') {
      for (const [tableName, sql] of Object.entries(tables)) {
        this.db.exec(sql);
      }
    }
  }

  // إدراج بيانات
  public async insert(table: string, data: any): Promise<string> {
    const id = this.generateId();
    
    if (this.dbType === 'memory') {
      this.db[table].set(id, { id, ...data, created_at: new Date() });
      return id;
    }

    const columns = Object.keys(data).join(', ');
    const placeholders = Object.keys(data).map(() => '?').join(', ');
    const values = Object.values(data);
    
    const sql = `INSERT INTO ${table} (id, ${columns}) VALUES (?, ${placeholders})`;

    if (this.dbType === 'sqlite3') {
      return new Promise((resolve, reject) => {
        this.db.run(sql, [id, ...values], function(err: any) {
          if (err) reject(err);
          else resolve(id);
        });
      });
    } else if (this.dbType === 'better-sqlite3') {
      const stmt = this.db.prepare(sql);
      stmt.run(id, ...values);
      return id;
    }

    return id;
  }

  // استعلام البيانات
  public async select(table: string, where?: any): Promise<any[]> {
    if (this.dbType === 'memory') {
      const results = Array.from(this.db[table].values());
      if (where) {
        return results.filter(item => {
          return Object.entries(where).every(([key, value]) => item[key] === value);
        });
      }
      return results;
    }

    let sql = `SELECT * FROM ${table}`;
    let params: any[] = [];

    if (where) {
      const conditions = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
      sql += ` WHERE ${conditions}`;
      params = Object.values(where);
    }

    if (this.dbType === 'sqlite3') {
      return new Promise((resolve, reject) => {
        this.db.all(sql, params, (err: any, rows: any) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });
    } else if (this.dbType === 'better-sqlite3') {
      const stmt = this.db.prepare(sql);
      return stmt.all(...params);
    }

    return [];
  }

  // تحديث البيانات
  public async update(table: string, id: string, data: any): Promise<boolean> {
    if (this.dbType === 'memory') {
      if (this.db[table].has(id)) {
        const existing = this.db[table].get(id);
        this.db[table].set(id, { ...existing, ...data, updated_at: new Date() });
        return true;
      }
      return false;
    }

    const columns = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const values = Object.values(data);
    const sql = `UPDATE ${table} SET ${columns}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;

    if (this.dbType === 'sqlite3') {
      return new Promise((resolve, reject) => {
        this.db.run(sql, [...values, id], function(err: any) {
          if (err) reject(err);
          else resolve(this.changes > 0);
        });
      });
    } else if (this.dbType === 'better-sqlite3') {
      const stmt = this.db.prepare(sql);
      const result = stmt.run(...values, id);
      return result.changes > 0;
    }

    return false;
  }

  // حذف البيانات
  public async delete(table: string, id: string): Promise<boolean> {
    if (this.dbType === 'memory') {
      return this.db[table].delete(id);
    }

    const sql = `DELETE FROM ${table} WHERE id = ?`;

    if (this.dbType === 'sqlite3') {
      return new Promise((resolve, reject) => {
        this.db.run(sql, [id], function(err: any) {
          if (err) reject(err);
          else resolve(this.changes > 0);
        });
      });
    } else if (this.dbType === 'better-sqlite3') {
      const stmt = this.db.prepare(sql);
      const result = stmt.run(id);
      return result.changes > 0;
    }

    return false;
  }

  // إنشاء معرف فريد
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // إغلاق قاعدة البيانات
  public close(): void {
    if (this.db && this.dbType !== 'memory') {
      if (this.dbType === 'sqlite3') {
        this.db.close();
      } else if (this.dbType === 'better-sqlite3') {
        this.db.close();
      }
    }
  }

  // الحصول على معلومات قاعدة البيانات
  public getInfo(): any {
    return {
      type: this.dbType,
      connected: this.db !== null,
      tables: this.dbType === 'memory' ? Object.keys(this.db) : ['projects', 'matches', 'events', 'players', 'teams']
    };
  }
}

// إتاحة الخدمة عالمياً
if (typeof window !== 'undefined') {
  (window as any).databaseService = DatabaseService.getInstance();
}

export default DatabaseService;
