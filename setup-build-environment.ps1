# Football Analysis Pro - Build Environment Setup
# PowerShell script to install all required build tools

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Football Analysis Pro - Build Setup  " -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "❌ This script requires administrator privileges!" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Running with administrator privileges" -ForegroundColor Green
Write-Host ""

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Function to download and install from URL
function Install-FromUrl($name, $url, $args) {
    Write-Host "📥 Downloading $name..." -ForegroundColor Blue
    $tempFile = [System.IO.Path]::GetTempFileName() + ".exe"
    
    try {
        Invoke-WebRequest -Uri $url -OutFile $tempFile -UseBasicParsing
        Write-Host "🔧 Installing $name..." -ForegroundColor Blue
        Start-Process -FilePath $tempFile -ArgumentList $args -Wait
        Remove-Item $tempFile -Force
        Write-Host "✅ $name installed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to install $name" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
    }
}

# 1. Install Chocolatey (Package Manager)
Write-Host "🍫 Installing Chocolatey..." -ForegroundColor Blue
if (-not (Test-Command choco)) {
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    Write-Host "✅ Chocolatey installed" -ForegroundColor Green
} else {
    Write-Host "✅ Chocolatey already installed" -ForegroundColor Green
}

# 2. Install Node.js LTS
Write-Host "🟢 Installing Node.js LTS..." -ForegroundColor Blue
if (-not (Test-Command node)) {
    choco install nodejs-lts -y
    Write-Host "✅ Node.js installed" -ForegroundColor Green
} else {
    $nodeVersion = node --version
    Write-Host "✅ Node.js already installed: $nodeVersion" -ForegroundColor Green
}

# 3. Install Python
Write-Host "🐍 Installing Python..." -ForegroundColor Blue
if (-not (Test-Command python)) {
    choco install python -y
    Write-Host "✅ Python installed" -ForegroundColor Green
} else {
    $pythonVersion = python --version
    Write-Host "✅ Python already installed: $pythonVersion" -ForegroundColor Green
}

# 4. Install Visual Studio Build Tools
Write-Host "🔨 Installing Visual Studio Build Tools..." -ForegroundColor Blue
$vsWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
if (-not (Test-Path $vsWhere)) {
    choco install visualstudio2022buildtools -y --params "--add Microsoft.VisualStudio.Workload.VCTools --add Microsoft.VisualStudio.Workload.MSBuildTools --add Microsoft.VisualStudio.Component.Windows10SDK.19041"
    Write-Host "✅ Visual Studio Build Tools installed" -ForegroundColor Green
} else {
    Write-Host "✅ Visual Studio Build Tools already installed" -ForegroundColor Green
}

# 5. Install Git
Write-Host "📦 Installing Git..." -ForegroundColor Blue
if (-not (Test-Command git)) {
    choco install git -y
    Write-Host "✅ Git installed" -ForegroundColor Green
} else {
    $gitVersion = git --version
    Write-Host "✅ Git already installed: $gitVersion" -ForegroundColor Green
}

# 6. Install Windows SDK
Write-Host "🪟 Installing Windows SDK..." -ForegroundColor Blue
choco install windows-sdk-10-version-2004-all -y

# 7. Configure npm for native modules
Write-Host "⚙️ Configuring npm for native modules..." -ForegroundColor Blue
npm config set msvs_version 2022
npm config set python python
npm install -g node-gyp
npm install -g windows-build-tools --vs2022

# 8. Install global npm packages
Write-Host "📦 Installing global npm packages..." -ForegroundColor Blue
npm install -g electron
npm install -g electron-builder
npm install -g concurrently
npm install -g wait-on

# 9. Refresh environment variables
Write-Host "🔄 Refreshing environment variables..." -ForegroundColor Blue
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    🎉 BUILD ENVIRONMENT READY! 🎉    " -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Verify installations
Write-Host "🔍 Verifying installations..." -ForegroundColor Blue
Write-Host ""

if (Test-Command node) {
    $nodeVer = node --version
    Write-Host "✅ Node.js: $nodeVer" -ForegroundColor Green
} else {
    Write-Host "❌ Node.js: Not found" -ForegroundColor Red
}

if (Test-Command npm) {
    $npmVer = npm --version
    Write-Host "✅ npm: $npmVer" -ForegroundColor Green
} else {
    Write-Host "❌ npm: Not found" -ForegroundColor Red
}

if (Test-Command python) {
    $pythonVer = python --version
    Write-Host "✅ Python: $pythonVer" -ForegroundColor Green
} else {
    Write-Host "❌ Python: Not found" -ForegroundColor Red
}

if (Test-Command git) {
    $gitVer = git --version
    Write-Host "✅ Git: $gitVer" -ForegroundColor Green
} else {
    Write-Host "❌ Git: Not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "🚀 Your system is now ready to build Football Analysis Pro!" -ForegroundColor Cyan
Write-Host "You can now run the build process to create the professional executable." -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to continue"
