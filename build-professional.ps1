# Football Analysis Pro - Professional Build Script
# Complete build process with native modules support

param(
    [switch]$SkipInstall,
    [switch]$SkipRebuild,
    [switch]$SkipBuild,
    [string]$Platform = "win32",
    [string]$Arch = "x64"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Football Analysis Pro - Build System " -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Blue = "Blue"
$Cyan = "Cyan"

# Function to run command with error handling
function Invoke-SafeCommand {
    param(
        [string]$Command,
        [string]$Description,
        [switch]$ContinueOnError
    )
    
    Write-Host "🔧 $Description..." -ForegroundColor $Blue
    try {
        Invoke-Expression $Command
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description completed successfully" -ForegroundColor $Green
            return $true
        } else {
            Write-Host "❌ $Description failed with exit code $LASTEXITCODE" -ForegroundColor $Red
            if (-not $ContinueOnError) {
                throw "Command failed: $Command"
            }
            return $false
        }
    }
    catch {
        Write-Host "❌ $Description failed: $($_.Exception.Message)" -ForegroundColor $Red
        if (-not $ContinueOnError) {
            throw
        }
        return $false
    }
}

# Step 1: Environment Setup
Write-Host "🌍 Setting up build environment..." -ForegroundColor $Cyan

# Set Node.js environment variables
$env:NODE_ENV = "production"
$env:ELECTRON_BUILDER_CACHE = "$env:USERPROFILE\.electron-builder"

# Set npm configuration for native modules
$electronVersion = "27.1.3"
$env:npm_config_target = $electronVersion
$env:npm_config_arch = $Arch
$env:npm_config_target_arch = $Arch
$env:npm_config_disturl = "https://electronjs.org/headers"
$env:npm_config_runtime = "electron"
$env:npm_config_build_from_source = "true"

Write-Host "✅ Environment configured for Electron $electronVersion" -ForegroundColor $Green

# Step 2: Clean previous builds
Write-Host "🧹 Cleaning previous builds..." -ForegroundColor $Cyan
if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist" -ErrorAction SilentlyContinue
}
if (Test-Path "build") {
    Remove-Item -Recurse -Force "build" -ErrorAction SilentlyContinue
}

# Step 3: Install dependencies (if not skipped)
if (-not $SkipInstall) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor $Cyan
    
    # Clean npm cache
    Invoke-SafeCommand "npm cache clean --force" "Cleaning npm cache" -ContinueOnError
    
    # Remove node_modules and package-lock.json
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force "node_modules" -ErrorAction SilentlyContinue
    }
    if (Test-Path "package-lock.json") {
        Remove-Item -Force "package-lock.json" -ErrorAction SilentlyContinue
    }
    
    # Install dependencies with specific configuration
    Invoke-SafeCommand "npm install --no-audit --no-fund" "Installing dependencies"
    
    # Install native modules separately with specific configuration
    Write-Host "🔨 Installing native modules..." -ForegroundColor $Blue
    
    # Install sqlite3
    $sqliteCmd = "npm install sqlite3@5.1.6 --build-from-source --target=$electronVersion --arch=$Arch --dist-url=https://electronjs.org/headers"
    $sqliteSuccess = Invoke-SafeCommand $sqliteCmd "Installing sqlite3" -ContinueOnError
    
    if (-not $sqliteSuccess) {
        Write-Host "🔄 Trying better-sqlite3 as alternative..." -ForegroundColor $Yellow
        Invoke-SafeCommand "npm install better-sqlite3@8.7.0" "Installing better-sqlite3" -ContinueOnError
    }
    
    # Install canvas
    $canvasCmd = "npm install canvas@2.11.2 --build-from-source --target=$electronVersion --arch=$Arch --dist-url=https://electronjs.org/headers"
    Invoke-SafeCommand $canvasCmd "Installing canvas" -ContinueOnError
    
    Write-Host "✅ Dependencies installation completed" -ForegroundColor $Green
}

# Step 4: Rebuild native modules (if not skipped)
if (-not $SkipRebuild) {
    Write-Host "🔨 Rebuilding native modules for Electron..." -ForegroundColor $Cyan
    
    # Install electron-rebuild if not present
    Invoke-SafeCommand "npm install electron-rebuild@3.2.9 --save-dev" "Installing electron-rebuild" -ContinueOnError
    
    # Rebuild all native modules
    Invoke-SafeCommand "npx electron-rebuild --version=$electronVersion --arch=$Arch" "Rebuilding native modules" -ContinueOnError
    
    # Rebuild specific modules
    Invoke-SafeCommand "npx electron-rebuild -f -w sqlite3" "Rebuilding sqlite3" -ContinueOnError
    Invoke-SafeCommand "npx electron-rebuild -f -w canvas" "Rebuilding canvas" -ContinueOnError
    
    Write-Host "✅ Native modules rebuild completed" -ForegroundColor $Green
}

# Step 5: Build React application (if not skipped)
if (-not $SkipBuild) {
    Write-Host "⚛️ Building React application..." -ForegroundColor $Cyan
    Invoke-SafeCommand "npm run build" "Building React app"
    Write-Host "✅ React build completed" -ForegroundColor $Green
}

# Step 6: Test native modules
Write-Host "🧪 Testing native modules..." -ForegroundColor $Cyan
$testScript = @"
try {
    const electron = require('electron');
    console.log('✅ Electron: OK');
} catch(e) {
    console.log('❌ Electron: ' + e.message);
}

try {
    const sqlite3 = require('sqlite3');
    console.log('✅ SQLite3: OK');
} catch(e) {
    try {
        const betterSqlite3 = require('better-sqlite3');
        console.log('✅ Better-SQLite3: OK');
    } catch(e2) {
        console.log('❌ SQLite: ' + e.message);
    }
}

try {
    const canvas = require('canvas');
    console.log('✅ Canvas: OK');
} catch(e) {
    console.log('❌ Canvas: ' + e.message);
}

try {
    const tf = require('@tensorflow/tfjs');
    console.log('✅ TensorFlow.js: OK');
} catch(e) {
    console.log('❌ TensorFlow.js: ' + e.message);
}
"@

$testResult = node -e $testScript
Write-Host $testResult

# Step 7: Build Electron application
Write-Host "⚡ Building Electron application..." -ForegroundColor $Cyan

# Configure electron-builder
$builderArgs = @(
    "--$Platform"
    "--$Arch"
    "--publish=never"
)

$buildCommand = "npx electron-builder " + ($builderArgs -join " ")
Invoke-SafeCommand $buildCommand "Building Electron app"

# Step 8: Verify build output
Write-Host "🔍 Verifying build output..." -ForegroundColor $Cyan

$distPath = "dist"
if (Test-Path $distPath) {
    $files = Get-ChildItem $distPath -Recurse
    Write-Host "📁 Build output files:" -ForegroundColor $Blue
    foreach ($file in $files) {
        if ($file.Extension -eq ".exe" -or $file.Extension -eq ".msi") {
            $sizeKB = [math]::Round($file.Length / 1KB, 2)
            Write-Host "  📦 $($file.Name) ($sizeKB KB)" -ForegroundColor $Green
        }
    }
} else {
    Write-Host "❌ No build output found in dist folder" -ForegroundColor $Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor $Green
Write-Host "    🎉 BUILD PROCESS COMPLETED! 🎉    " -ForegroundColor $Green
Write-Host "========================================" -ForegroundColor $Green
Write-Host ""

# Final summary
Write-Host "📋 Build Summary:" -ForegroundColor $Cyan
Write-Host "  • Platform: $Platform" -ForegroundColor $Blue
Write-Host "  • Architecture: $Arch" -ForegroundColor $Blue
Write-Host "  • Electron Version: $electronVersion" -ForegroundColor $Blue
Write-Host "  • Output Directory: $distPath" -ForegroundColor $Blue
Write-Host ""

if (Test-Path "$distPath\win-unpacked") {
    Write-Host "🚀 Portable version available in: $distPath\win-unpacked" -ForegroundColor $Green
}

$setupFiles = Get-ChildItem $distPath -Filter "*Setup*.exe" -ErrorAction SilentlyContinue
if ($setupFiles) {
    Write-Host "💿 Installer available: $($setupFiles[0].Name)" -ForegroundColor $Green
}

Write-Host ""
Write-Host "✅ Football Analysis Pro is ready for distribution!" -ForegroundColor $Cyan

Read-Host "Press Enter to exit"
