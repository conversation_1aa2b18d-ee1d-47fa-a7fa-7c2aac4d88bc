# ⚽ Football Analysis Pro

## 🚀 برنامج تحليل مباريات كرة القدم الاحترافي - إصدار EXE مستقل

### 🎯 نظرة عامة
Football Analysis Pro هو برنامج **احترافي مستقل** لتحليل مباريات كرة القدم، مصمم ليعمل كبرنامج .exe عادي بدون الحاجة لأي ملفات تشغيل خارجية أو سكريبتات. البرنامج مستوحى من Metrica Nexus ومطور بأحدث التقنيات.

### ✨ المميزات الجديدة في الإصدار المستقل

#### 🔥 برنامج مستقل 100%
- **ملف .exe واحد**: لا حاجة لملفات .bat أو سكريبتات
- **تثبيت احترافي**: installer متقدم مع NSIS
- **نسخة محمولة**: تعمل بدون تثبيت
- **تحديث تلقائي**: نظام تحديث مدمج
- **أمان متقدم**: توقيع رقمي وشهادات أمان

#### 🛠️ تقنيات متقدمة
- **مكتبات محسنة**: حل مشاكل sqlite3 و canvas
- **أداء محسن**: استخدام أمثل للذاكرة والمعالج
- **دعم شامل**: جميع المكتبات المعقدة مدمجة
- **استقرار عالي**: اختبار شامل على أنظمة متعددة

### 🚀 التثبيت والتشغيل الجديد

#### الطريقة الوحيدة المطلوبة
```powershell
# تشغيل سكريبت البناء الشامل
.\BUILD_COMPLETE_EXE.ps1
```

**هذا كل شيء!** 🎉

#### ما سيحدث تلقائياً:
1. ✅ تثبيت جميع المتطلبات (Node.js, Python, Visual Studio Build Tools)
2. ✅ حل مشاكل المكتبات المعقدة (sqlite3, canvas, TensorFlow)
3. ✅ بناء React application
4. ✅ إنشاء Electron executable
5. ✅ إنشاء installer احترافي
6. ✅ إنشاء نسخة محمولة
7. ✅ تنظيم الملفات النهائية

### 📦 الملفات النهائية

بعد البناء ستجد في مجلد `FINAL_DISTRIBUTION`:

```
FINAL_DISTRIBUTION/
├── Football Analysis Pro Setup.exe    # المثبت الاحترافي
├── Portable/                          # النسخة المحمولة
│   └── Football Analysis Pro.exe      # الملف التنفيذي
├── README.md                          # دليل المستخدم
├── LICENSE.txt                        # الترخيص
└── INSTRUCTIONS.md                    # تعليمات التشغيل
```

### 🎯 الميزات الأساسية

#### 🎥 مشغل فيديو متقدم
- دعم جميع تنسيقات الفيديو الشائعة
- تحكم دقيق (إطار بالإطار)
- اختصارات لوحة المفاتيح
- وضع ملء الشاشة

#### 🏷️ نظام ترميز الأحداث
- Coding Pad قابل للتخصيص
- قوالب جاهزة للأحداث
- اختصارات سريعة
- ترميز مستمر

#### ✏️ أدوات الرسم والتأشير
- أدوات رسم متنوعة
- تخصيص كامل للألوان والسماكة
- رسم فوق الفيديو مباشرة
- حفظ التعليقات مع الوقت

#### 🎯 نظام التتبع
- تتبع يدوي للاعبين والكرة
- تتبع تلقائي بالذكاء الاصطناعي
- إدارة الفرق واللاعبين
- رسم مسارات الحركة

#### 🤖 الذكاء الاصطناعي
- كشف اللاعبين والكرة تلقائياً
- تحليل الحركة والأنماط
- إحصائيات متقدمة
- تحسين دقة التتبع

#### 📊 التحليل المباشر
- إحصائيات فورية
- لوحة إحصائيات متقدمة
- مقارنة أداء الفرق
- نظام إشعارات

#### 📁 إدارة المشاريع
- إنشاء وحفظ مشاريع
- استيراد وتصدير البيانات
- نسخ احتياطية تلقائية
- إحصائيات شاملة

#### 📤 التصدير والمشاركة
- تصدير بصيغ متعددة
- مشاركة عبر منصات مختلفة
- تقارير احترافية
- تصدير مقاطع فيديو

## التقنيات المستخدمة

### Frontend
- **React.js** مع TypeScript
- **Material-UI** للواجهة
- **Video.js** لتشغيل الفيديو
- **Fabric.js** لأدوات الرسم
- **TensorFlow.js** للذكاء الاصطناعي

### Backend
- **Node.js** مع Express
- **SQLite** لقاعدة البيانات
- **FFmpeg** لمعالجة الفيديو

### Desktop App
- **Electron** لتطبيق سطح المكتب

## متطلبات النظام

### الحد الأدنى
- نظام التشغيل: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- المعالج: Intel i5 أو AMD Ryzen 5
- الذاكرة: 8 GB RAM
- كرت الرسوميات: مدمج أو منفصل
- مساحة التخزين: 2 GB

### المستوى المتقدم (للتتبع التلقائي)
- المعالج: Intel i7 أو AMD Ryzen 7
- الذاكرة: 16 GB RAM
- كرت الرسوميات: NVIDIA GTX 1060 أو أفضل
- مساحة التخزين: 5 GB

## التثبيت والتشغيل

### الطريقة الأبسط (مضمونة 100%)
1. **انقر نقراً مزدوجاً على `basic-start.bat`**
   - تشغيل مباشر بدون تعقيدات
   - أبسط طريقة ممكنة

### الطريقة البسيطة (مستحسنة)
1. **انقر نقراً مزدوجاً على `simple-start.bat`**
   - تشغيل مع فحص أساسي
   - تثبيت تلقائي إذا لزم الأمر

### الطريقة الشاملة
1. **انقر نقراً مزدوجاً على `quick-start.bat`**
   - تشغيل مع تثبيت تلقائي
   - رسائل مفصلة

### الطريقة اليدوية
1. **تثبيت المتطلبات:**
   ```bash
   npm install
   ```

2. **تشغيل البرنامج:**
   ```bash
   npm run dev
   ```

3. **بناء التطبيق للإنتاج:**
   ```bash
   npm run build-electron
   ```

### ملفات التشغيل المساعدة
- `basic-start.bat` - تشغيل مباشر (الأبسط) ⭐
- `simple-start.bat` - تشغيل بسيط (مستحسن) ⭐⭐
- `quick-start.bat` - تشغيل مع تثبيت تلقائي ⭐⭐⭐
- `start.bat` - تشغيل شامل مع فحص النظام ⭐⭐⭐⭐
- `install.bat` - تثبيت المتطلبات فقط
- `run.bat` - تشغيل البرنامج فقط
- `test.bat` - اختبار البرنامج والتحقق من سلامته

## هيكل المشروع

```
football-analysis-app/
├── src/                    # كود المصدر الرئيسي
│   ├── components/         # مكونات React
│   ├── pages/             # صفحات التطبيق
│   ├── utils/             # وظائف مساعدة
│   ├── services/          # خدمات API
│   ├── hooks/             # React Hooks مخصصة
│   ├── contexts/          # React Contexts
│   └── types/             # تعريفات TypeScript
├── backend/               # خادم Backend
│   ├── routes/            # مسارات API
│   ├── models/            # نماذج قاعدة البيانات
│   ├── controllers/       # تحكم في العمليات
│   └── middleware/        # وسطاء Express
├── public/                # ملفات عامة
├── assets/                # الأصول (صور، أيقونات)
└── docs/                  # التوثيق
```

## المساهمة
نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## الترخيص
هذا المشروع مرخص تحت رخصة MIT.

## الدعم
للحصول على الدعم، يرجى فتح issue في GitHub أو التواصل معنا.
