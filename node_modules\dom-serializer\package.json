{"name": "dom-serializer", "version": "1.4.1", "description": "render domhandler DOM nodes to a string", "author": "<PERSON> <<EMAIL>>", "sideEffects": false, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/**/*"], "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "devDependencies": {"@types/jest": "^26.0.23", "@types/node": "^15.3.0", "@typescript-eslint/eslint-plugin": "^4.23.0", "@typescript-eslint/parser": "^4.23.0", "cheerio": "^1.0.0-rc.9", "coveralls": "^3.0.5", "eslint": "^7.26.0", "eslint-config-prettier": "^8.3.0", "htmlparser2": "^6.1.0", "jest": "^26.0.1", "prettier": "^2.3.0", "ts-jest": "^26.5.6", "typescript": "^4.0.2"}, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "funding": "https://github.com/cheeriojs/dom-serializer?sponsor=1", "license": "MIT"}