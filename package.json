{"name": "football-analysis-app", "version": "1.0.0", "description": "برنامج تحليل مباريات كرة القدم مشابه لـ Metrica Nexus", "main": "public/electron.js", "homepage": "./", "scripts": {"start": "react-scripts start", "dev": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "build-electron": "npm run build && electron-builder", "build-exe": "npm run build && electron-builder --win --x64", "build-installer": "npm run build && electron-builder --win --x64 --publish=never", "build-portable": "npm run build && electron-builder --win --x64 --dir", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps", "rebuild": "electron-rebuild", "rebuild-sqlite": "electron-rebuild -f -w sqlite3", "rebuild-canvas": "electron-rebuild -f -w canvas"}, "keywords": ["football", "analysis", "video", "sports", "tracking", "telestration"], "author": "Football Analysis Team", "license": "MIT", "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-data-grid": "^6.18.2", "@tensorflow/tfjs": "^4.15.0", "canvas": "^2.11.2", "cors": "^2.8.5", "express": "^4.18.2", "fabric": "^5.3.0", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "video.js": "^8.6.1", "videojs-contrib-hls": "^5.15.0"}, "devDependencies": {"@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/uuid": "^9.0.7", "concurrently": "^8.2.2", "electron": "^27.1.3", "electron-builder": "^24.13.3", "electron-is-dev": "^2.0.0", "electron-rebuild": "^3.2.9", "electron-updater": "^6.1.7", "typescript": "^4.9.5", "wait-on": "^7.2.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.footballanalysis.app", "productName": "Football Analysis Pro", "copyright": "Copyright © 2025 Football Analysis Team", "directories": {"output": "dist", "buildResources": "assets"}, "files": ["build/**/*", "public/electron.js", "node_modules/**/*", "!node_modules/.cache", "!node_modules/electron-builder", "!node_modules/electron-rebuild", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "asarUnpack": ["node_modules/sqlite3/**/*", "node_modules/canvas/**/*", "node_modules/@tensorflow/**/*"], "nodeGypRebuild": false, "buildDependenciesFromSource": true, "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "mac": {"category": "public.app-category.sports", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icons/app-icon.icns"}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icons/app-icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "category": "Sports", "icon": "assets/icons/app-icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Football Analysis Pro", "uninstallDisplayName": "Football Analysis Pro", "license": "LICENSE.txt", "installerIcon": "assets/icons/app-icon.ico", "uninstallerIcon": "assets/icons/app-icon.ico", "installerHeaderIcon": "assets/icons/app-icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Sports", "artifactName": "${productName}-Setup-${version}.${ext}"}, "portable": {"artifactName": "${productName}-Portable-${version}.${ext}"}, "compression": "maximum", "publish": null}}