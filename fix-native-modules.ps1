# Football Analysis Pro - Native Modules Fix
# PowerShell script to fix sqlite3, canvas and other native modules

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Fixing Native Modules for Electron   " -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️ Running without administrator privileges" -ForegroundColor Yellow
    Write-Host "Some operations may require elevated permissions" -ForegroundColor Yellow
}

# Function to run npm command with error handling
function Run-NpmCommand($command, $description) {
    Write-Host "🔧 $description..." -ForegroundColor Blue
    try {
        Invoke-Expression "npm $command"
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $description completed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $description failed with exit code $LASTEXITCODE" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ $description failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 1. Clean npm cache and node_modules
Write-Host "🧹 Cleaning npm cache and node_modules..." -ForegroundColor Blue
npm cache clean --force
if (Test-Path "node_modules") {
    Remove-Item -Recurse -Force "node_modules" -ErrorAction SilentlyContinue
}
if (Test-Path "package-lock.json") {
    Remove-Item -Force "package-lock.json" -ErrorAction SilentlyContinue
}

# 2. Set npm configuration for native modules
Write-Host "⚙️ Configuring npm for native modules..." -ForegroundColor Blue
npm config set target_arch x64
npm config set target_platform win32
npm config set arch x64
npm config set target_libc unknown
npm config set disturl https://electronjs.org/headers
npm config set runtime electron
npm config set cache_lock_stale 60000
npm config set node_gyp ((npm config get prefix) + "\node_modules\node-gyp\bin\node-gyp.js")

# 3. Install electron first
Write-Host "⚡ Installing Electron..." -ForegroundColor Blue
Run-NpmCommand "install electron@27.1.3 --save-dev" "Electron installation"

# 4. Get Electron version for native modules
$electronVersion = "27.1.3"
Write-Host "🔍 Using Electron version: $electronVersion" -ForegroundColor Blue

# 5. Set environment variables for native compilation
$env:npm_config_target = $electronVersion
$env:npm_config_arch = "x64"
$env:npm_config_target_arch = "x64"
$env:npm_config_disturl = "https://electronjs.org/headers"
$env:npm_config_runtime = "electron"
$env:npm_config_build_from_source = "true"

# 6. Install sqlite3 with specific configuration
Write-Host "🗄️ Installing sqlite3 for Electron..." -ForegroundColor Blue
$sqlite3Success = Run-NpmCommand "install sqlite3@5.1.6 --build-from-source --target=$electronVersion --arch=x64 --dist-url=https://electronjs.org/headers" "sqlite3 installation"

if (-not $sqlite3Success) {
    Write-Host "🔄 Trying alternative sqlite3 installation..." -ForegroundColor Yellow
    Run-NpmCommand "install better-sqlite3@8.7.0" "better-sqlite3 as alternative"
}

# 7. Install canvas with specific configuration
Write-Host "🎨 Installing canvas for Electron..." -ForegroundColor Blue
$canvasSuccess = Run-NpmCommand "install canvas@2.11.2 --build-from-source --target=$electronVersion --arch=x64 --dist-url=https://electronjs.org/headers" "canvas installation"

if (-not $canvasSuccess) {
    Write-Host "🔄 Trying to install canvas dependencies..." -ForegroundColor Yellow
    # Install canvas dependencies for Windows
    Run-NpmCommand "install --global --production windows-build-tools@4.0.0" "Windows build tools"
    Run-NpmCommand "install canvas@2.11.2 --build-from-source" "canvas with build tools"
}

# 8. Install other dependencies
Write-Host "📦 Installing remaining dependencies..." -ForegroundColor Blue
Run-NpmCommand "install" "remaining dependencies"

# 9. Rebuild native modules for Electron
Write-Host "🔨 Rebuilding native modules for Electron..." -ForegroundColor Blue
Run-NpmCommand "run postinstall" "native modules rebuild"

# Alternative rebuild methods
Write-Host "🔄 Trying electron-rebuild..." -ForegroundColor Blue
npm install electron-rebuild --save-dev
npx electron-rebuild

# 10. Verify installations
Write-Host ""
Write-Host "🔍 Verifying native modules..." -ForegroundColor Blue

# Check sqlite3
if (Test-Path "node_modules\sqlite3") {
    Write-Host "✅ sqlite3 module found" -ForegroundColor Green
} elseif (Test-Path "node_modules\better-sqlite3") {
    Write-Host "✅ better-sqlite3 module found (alternative)" -ForegroundColor Green
} else {
    Write-Host "❌ No SQLite module found" -ForegroundColor Red
}

# Check canvas
if (Test-Path "node_modules\canvas") {
    Write-Host "✅ canvas module found" -ForegroundColor Green
} else {
    Write-Host "❌ canvas module not found" -ForegroundColor Red
}

# Check electron
if (Test-Path "node_modules\electron") {
    Write-Host "✅ electron module found" -ForegroundColor Green
} else {
    Write-Host "❌ electron module not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    Native Modules Setup Complete!     " -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Test basic functionality
Write-Host "🧪 Testing basic functionality..." -ForegroundColor Blue
try {
    $testResult = node -e "
        try {
            const electron = require('electron');
            console.log('✅ Electron: OK');
        } catch(e) {
            console.log('❌ Electron: FAILED');
        }
        
        try {
            const sqlite3 = require('sqlite3');
            console.log('✅ SQLite3: OK');
        } catch(e) {
            try {
                const betterSqlite3 = require('better-sqlite3');
                console.log('✅ Better-SQLite3: OK');
            } catch(e2) {
                console.log('❌ SQLite: FAILED');
            }
        }
        
        try {
            const canvas = require('canvas');
            console.log('✅ Canvas: OK');
        } catch(e) {
            console.log('❌ Canvas: FAILED');
        }
    "
    
    Write-Host $testResult
} catch {
    Write-Host "❌ Test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🚀 Native modules are now ready for Electron build!" -ForegroundColor Cyan
Write-Host "You can proceed with building the application." -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to continue"
